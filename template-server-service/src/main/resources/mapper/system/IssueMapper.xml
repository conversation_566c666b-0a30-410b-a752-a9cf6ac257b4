<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.IssueMapper">

    <resultMap id="issueMap" type="com.youying.system.domain.issue.IssueResponse">
        <id column="id" property="id"/>
        <association property="issueResponse" select="replyIssueQuery" column="id" javaType="com.youying.system.domain.issue.IssueResponse" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Issue">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="repertoire_info_detail_id" property="repertoireInfoDetailId"/>
        <result column="user_id" property="userId"/>
        <result column="reply_id" property="replyId"/>
        <result column="parent_id" property="parentId"/>
        <result column="content" property="content"/>
        <result column="top" property="top"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, theater_id, repertoire_id, repertoire_info_detail_id, user_id, reply_id, parent_id, content, top, `status`, create_by, create_time
    </sql>

    <select id="replyIssueQuery" resultType="com.youying.system.domain.issue.IssueResponse">
        SELECT
            i.id,
            i.user_merchant_id,
            ( CASE i.user_merchant_id WHEN 0 THEN u.`name` ELSE mu.`name` END ) AS userName,
            ( CASE i.user_merchant_id WHEN 0 THEN u.`avatar` ELSE mu.`avatar` END ) AS userAvatar,
            rmi.rank_medal_name,
            rmi.`name` AS rankMedalLevel,
            rmi.color,
            i.content AS lastContent,
            i.create_time
        FROM
            t_issue AS i
            LEFT JOIN t_user AS u ON u.id = i.reply_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
            LEFT JOIN t_merchant_user AS mu ON mu.id = i.user_merchant_id
        WHERE
            i.parent_id = #{id}
        ORDER BY
            i.create_time DESC
            LIMIT 1
    </select>

    <select id="listByPage" resultMap="issueMap">
        SELECT
            i.id,
            i.content,
            i.create_time,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            rm.`name` AS rankMedalName,
            rmi.`name` AS rankMedalLevel,
            rmi.color,
            COUNT( DISTINCT i1.id ) AS replyCount
        FROM
            t_issue AS i
            LEFT JOIN t_user AS u ON u.id = i.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
            LEFT JOIN t_rank_medal AS rm ON rm.id = rmi.rank_medal_id
            LEFT JOIN t_issue AS i1 ON i1.parent_id = i.id
        <where>
            i.`status` = '1'
            AND i.parent_id = 0
            <if test="theaterId != null">
                AND i.theater_id = #{theaterId}
            </if>
            <if test="repertoireId != null">
                AND i.repertoire_id = #{repertoireId}
            </if>
        </where>
        GROUP BY
            i.id
        ORDER BY
            i.top DESC , i.create_time DESC
    </select>

    <select id="listByParentId" resultType="com.youying.system.domain.issue.IssueResponse">
        SELECT
            i.id,
            i.content,
            i.create_time,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            rm.`name` AS rankMedalName,
            rmi.`name` AS rankMedalLevel,
            rmi.color,
            i.user_merchant_id,
            mu.`name` AS merchantUserName,
            mu.avatar AS merchantUserAvatar
        FROM
            t_issue AS i
            LEFT JOIN t_user AS u ON u.id = i.reply_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
            LEFT JOIN t_rank_medal AS rm ON rm.id = rmi.rank_medal_id
            LEFT JOIN t_issue AS i1 ON i1.parent_id = i.id
            LEFT JOIN t_merchant_user AS mu ON mu.id = i.user_merchant_id
        WHERE
            i.parent_id = #{parentId}
            AND i.`status` = 1
        GROUP BY
            i.id
        ORDER BY
            i.top DESC
    </select>

</mapper>

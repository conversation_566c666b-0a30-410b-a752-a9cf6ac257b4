<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserReceivingRecordsMapper">

    <resultMap id="listByDigitalAvatarMap" type="com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse">
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="user_id" property="userId"/>
        <collection property="userReceivingRecordsList" select="digitalAvatarQuery" column="{repertoireId=repertoireId,userId=userId}" ofType="com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse" />
    </resultMap>

    <resultMap id="listByRankMedalMap" type="UserReceivingRecordsResponse">
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="userId" property="userId"/>
        <collection property="userReceivingRecordsList" select="rankMedalQuery" column="{theaterId=theaterId,repertoireId=repertoireId,userId=userId}" ofType="UserReceivingRecordsResponse" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserReceivingRecords">
        <id column="id" property="id"/>
        <result column="no" property="no"/>
        <result column="send_id" property="sendId"/>
        <result column="user_id" property="userId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="relation_id" property="relationId"/>
        <result column="badge_type" property="badgeType"/>
        <result column="seat_number" property="seatNumber"/>
        <result column="amount" property="amount"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , `no`, send_id, user_id, theater_id, repertoire_id, relation_id, badge_type, seat_number, amount, create_by, create_time, update_by, update_time
    </sql>

    <update id="updateUserReceivingRecord">
        update t_user_receiving_records
        <set>
            <if test="userReceivingRecords.orderId != null">
                order_id = #{userReceivingRecords.orderId},
            </if>
            <if test="userReceivingRecords.orderNo != null and userReceivingRecords.orderNo != ''">
                order_no = #{userReceivingRecords.orderNo},
            </if>
            <if test="userReceivingRecords.upgradeStatus != null">
                upgrade_status = #{userReceivingRecords.upgradeStatus},
            </if>
            <if test="userReceivingRecords.upgradeTime != null">
                upgrade_time = #{userReceivingRecords.upgradeTime},
            </if>
            <if test="userReceivingRecords.upgradeImage != null and userReceivingRecords.upgradeImage != ''">
                upgrade_image = #{userReceivingRecords.upgradeImage},
            </if>
        </set>
        where id = #{userReceivingRecords.id}
    </update>

    <select id="digitalAvatarQuery" resultType="UserReceivingRecordsResponse">
        SELECT
            *
        FROM
            (
                SELECT
                    urr.id ,
                    urr.image
                FROM
                    t_user_receiving_records AS urr
                WHERE
                    urr.user_id = #{userId}
                    AND badge_type = 2
                    AND urr.repertoire_id = #{repertoireId}
                UNION
                SELECT
                    urr.id ,
                    urr.upgrade_image AS image
                FROM
                    t_user_receiving_records AS urr
                WHERE
                    urr.user_id = #{userId}
                    AND badge_type = 2
                    AND urr.repertoire_id = #{repertoireId}
                    AND urr.upgrade_status = 1
            ) AS urr
    </select>

    <select id="rankMedalQuery" resultType="UserReceivingRecordsResponse">
        SELECT
            urr.id,
            urr.relation_id,
            urr.image,
            rmi.rank_medal_name,
            rmi.`name` AS rankMedalLevel,
            rmi.color AS rankMedalColor,
            rmi.expense_price,
            rmi.expense_number,
            urr.create_time
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = urr.relation_id
        WHERE
            urr.user_id = #{userId}
            AND urr.badge_type = 4
            <if test="theaterId != null">
                AND urr.theater_id = #{theaterId}
            </if>
            <if test="repertoireId != null">
                AND urr.repertoire_id = #{repertoireId}
            </if>
        ORDER BY urr.create_time DESC , rmi.id DESC
        LIMIT 1
    </select>

    <select id="listByPage"
            resultType="com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse">
        SELECT
            urr.id,
            urr.`no`,
            urr.send_id,
            urr.collection_no,
            urr.actor_information,
            t.`name` AS theater_name,
            r.`name` AS repertoire_name,
            r.cover_picture,
            (SELECT GROUP_CONCAT( DISTINCT `name`) FROM t_repertoire_label WHERE repertoire_id = r.id) AS repertoireLabel,
            urr.time,
            urr.image,
            urr.upgrade_image,
            urr.amount,
            urr.issuer_name,
            urr.relation_id,
            t.city_id,
            a.`fullname` AS city_name,
            urr.create_time,
            urr.upgrade_status,
            urr.upgrade_time,
            urr.portfolio_id,
            urr.portfolio_no,
            urr.price,
            pi.cover_front
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_portfolio_info AS pi ON pi.id = urr.portfolio_info_id
            LEFT JOIN t_theater AS t ON t.id = urr.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            LEFT JOIN t_area AS a ON a.id = t.city_id
        <where>
            urr.user_id = #{userId}
            <if test="badgeType != null">
                AND urr.badge_type = #{badgeType}
            </if>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (SELECT COUNT(1) FROM t_user_receiving_records_text WHERE user_receiving_records_id = urr.id AND actor_information LIKE CONCAT('%',#{keyword},'%') ) > 0
                )
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(urr.`time`) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="getTime.beginTime != null and getTime.endTime != null">
                AND DATE(urr.`create_time`) BETWEEN #{getTime.beginTime} AND #{getTime.endTime}
            </if>
            <if test="ticketGroupId != null">
                AND (SELECT COUNT(1) FROM t_user_ticket_group WHERE ticket_group_id = #{ticketGroupId} AND user_id = #{userId} AND user_receiving_records_id = urr.id) > 0
            </if>
            <if test="adorn != null">
                AND urr.adorn = #{adorn}
            </if>
        </where>
    </select>

    <select id="findSumPrice" resultType="java.math.BigDecimal">
        SELECT
            SUM(urr.price)
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_portfolio_info AS pi ON pi.id = urr.portfolio_info_id
            LEFT JOIN t_theater AS t ON t.id = pi.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = pi.repertoire_id
        <where>
            urr.user_id = #{userId}
            <if test="badgeType != null">
                AND urr.badge_type = #{badgeType}
            </if>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (SELECT COUNT(1) FROM t_user_receiving_records_text WHERE user_receiving_records_id = urr.id AND actor_information LIKE CONCAT('%',#{keyword},'%') ) > 0
                )
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(urr.`time`) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="getTime.beginTime != null and getTime.endTime != null">
                AND DATE(urr.`create_time`) BETWEEN #{getTime.beginTime} AND #{getTime.endTime}
            </if>
            <if test="ticketGroupId != null">
                AND (SELECT COUNT(1) FROM t_user_ticket_group WHERE ticket_group_id = #{ticketGroupId} AND user_id = #{userId} AND user_receiving_records_id = urr.id) > 0
            </if>
            <if test="adorn != null">
                AND urr.adorn = #{adorn}
            </if>
        </where>
    </select>

    <select id="findRandomUser" resultType="java.lang.Long">
        SELECT
            DISTINCT urr.user_id
        FROM
            t_user_receiving_records AS urr
        <where>
            <if test="repertoireId != null">
                AND urr.repertoire_id = #{repertoireId}
            </if>
            <if test="theaterId != null">
                AND urr.theater_id = #{theaterId}
            </if>
            <if test="userId != null">
                AND urr.user_id != #{userId}
            </if>
        </where>
        ORDER BY RAND()
        LIMIT 5
    </select>

    <select id="listByDigitalAvatar" resultMap="listByDigitalAvatarMap">
        SELECT
            urr.repertoire_id AS repertoireId,
            r.`name` AS repertoireName,
            #{userId} AS userId
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
        <where>
            badge_type = 2
            <if test="userId != null">
                AND urr.user_id = #{userId}
            </if>
        </where>
        GROUP BY
            urr.repertoire_id
    </select>

    <select id="listByRankMedal" resultMap="listByRankMedalMap">
        SELECT
            urr.theater_id AS theaterId,
            urr.repertoire_id AS repertoireId,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            MAX(urr.create_time) AS create_time,
            #{userId} AS userId
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_theater AS t ON t.id = urr.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
        <where>
            badge_type = 4
            <if test="userId != null">
                AND urr.user_id = #{userId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
        </where>
        GROUP BY
            urr.theater_id,urr.repertoire_id
        ORDER BY
            create_time DESC
    </select>

    <select id="detailsByRepertoireTicket"
            resultType="com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse">
        SELECT
            urr.id,
            urr.image,
            urr.`no`,
            urr.collection_no,
            urr.send_id,
            urr.amount,
            urr.seat_number,
            urr.issuer_name,
            r.`name` AS repertoireName,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            urr.create_time
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            LEFT JOIN t_user AS u ON u.id = urr.user_id
        WHERE
            urr.id = #{id}
            AND urr.user_id = #{userId}
            AND urr.badge_type = #{badgeType}
    </select>

    <select id="detailsByDigitalAvatar" resultType="UserReceivingRecordsResponse">
        SELECT
            urr.id,
            urr.image,
            urr.`no`,
            p.no AS portfolioNo,
            urr.collection_no,
            urr.send_id,
            urr.issuer_name,
            r.`name` AS repertoireName ,
            p.issued_quantity AS digitalAvatarIssuedQuantity,
            p.start_time,
            p.end_time,
            urr.create_time,
            ( SELECT id FROM t_user_adorn_collection WHERE user_receiving_records_id = urr.id AND upgrade_status = #{upgradeStatus} ) AS adorn,
            da.qr_code,
            urr.upgrade_image,
            urr.upgrade_time,
            urr.upgrade_status
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            LEFT JOIN t_user AS u ON u.id = urr.user_id
            LEFT JOIN t_digital_avatar AS da ON da.id = urr.relation_id
            LEFT JOIN t_portfolio AS p ON p.id = urr.portfolio_id
        WHERE
            urr.id = #{id}
            AND urr.user_id = #{userId}
            AND urr.badge_type = #{badgeType}
    </select>

    <select id="detailsBySouvenirBadge" resultType="UserReceivingRecordsResponse">
        SELECT
            urr.id,
            urr.image,
            urr.`no`,
            urr.theater_id,
            urr.collection_no,
            urr.send_id,
            sb.issuer_name,
            sb.model,
            t.`name` AS theaterName,
            sb.cover_picture,
            ( SELECT id FROM t_user_adorn_collection WHERE user_receiving_records_id = urr.id ) AS adorn,
            sb.`name` AS souvenirBadgeName,
            sb.issued_quantity AS souvenirBadgeIssuedQuantity,
            urr.create_time,
            t.qr_code
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_theater AS t ON t.id = urr.theater_id
            LEFT JOIN t_user AS u ON u.id = urr.user_id
            LEFT JOIN t_souvenir_badge AS sb ON sb.id = urr.relation_id
        WHERE
            urr.id = #{id}
            AND urr.user_id = #{userId}
            AND urr.badge_type = #{badgeType}
    </select>

    <select id="detailsByRankMedal" resultType="UserReceivingRecordsResponse">
        SELECT
            urr.id,
            urr.image,
            urr.`no`,
            urr.collection_no,
            urr.send_id,
            urr.issuer_name,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            urr.create_time,
            urr.relation_id,
            rmi.rank_medal_name,
            rmi.`name` AS rankMedalLevel,
            rmi.color AS rankMedalColor,
            rmi.expense_price,
            rmi.expense_number
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_theater AS t ON t.id = urr.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            LEFT JOIN t_user AS u ON u.id = urr.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = urr.relation_id
        WHERE
            urr.id = #{id}
            AND urr.user_id = #{userId}
            AND urr.badge_type = #{badgeType}
    </select>

    <select id="findUserReceivingRecords"
            resultType="com.youying.common.core.domain.entity.UserReceivingRecords">
        SELECT
            *
        FROM
            t_user_receiving_records AS urr
        WHERE
            urr.send_id is null
            AND drop_id is not null
            AND DATE_SUB(NOW(),INTERVAL 4 MINUTE) >= create_time
    </select>

    <select id="details"
            resultType="com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse">
        SELECT
            urr.`id`,
            urr.`no`,
            urr.`sku_id`,
            urr.`drop_id`,
            urr.`collection_no`,
            urr.`send_id`,
            urr.`user_id`,
            urr.`theater_id`,
            urr.`repertoire_id`,
            urr.`repertoire_info_detail_id`,
            urr.`repertoire_info_id`,
            urr.`relation_id`,
            urr.`badge_type`,
            urr.`issuer_name`,
            urr.`issued_quantity`,
            urr.`start_time`,
            urr.`end_time`,
            urr.`image`,
            urr.`seat_number`,
            urr.`amount`,
            urr.`time`,
            urr.`comment_id`,
            urr.`two_comment_id`,
            urr.`look_flag`,
            urr.`create_by`,
            urr.`adorn`,
            urr.`create_time`,
            urr.`update_by`,
            urr.`update_time`,
            pi.cover_front,
            pi.cover_reverse,
            pi1.cover_reverse AS upgradeCoverReverse,
            r.`name` AS repertoireName,
            r.cover_picture AS repertoireCoverPicture,
            t.`name` AS theaterName,
            t.cover_picture AS theaterCoverPicture,
            urr.no AS portfolioNo,
            urr.upgrade_image,
            urr.upgrade_time,
            urr.upgrade_status,
            urr.portfolio_id,
            urr.portfolio_no
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_repertoire_ticket AS rk ON rk.id = urr.relation_id AND badge_type = 1
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = urr.theater_id
            LEFT JOIN t_portfolio_info AS pi ON pi.portfolio_id = urr.portfolio_id AND pi.status = 1
            LEFT JOIN t_portfolio_info AS pi1 ON pi1.id = urr.portfolio_info_id
        WHERE
            urr.id = #{id}
    </select>

    <select id="findIsPickedUp" resultType="java.lang.Long">
        SELECT
            COUNT( 1 )
        FROM
            t_user_receiving_records AS urr
        <where>
            urr.portfolio_id = #{portfolioId}
            AND badge_type = 1
            AND urr.seat_number = #{seat}
            <if test="timeList != null and timeList.size() > 0">
                AND
                <foreach item="item" index="index" collection="timeList"
                         open="(" separator="OR" close=")" nullable="true">
                    (
                    (DATE_FORMAT( urr.time, '%Y%c%e%H%i' ) = #{item})
                    OR
                    (DATE_FORMAT( urr.time, '%Y%m%d%H%i' ) = #{item})
                    OR
                    (DATE_FORMAT( urr.time, '%Y%m%e%H%i' ) = #{item})
                    OR
                    (DATE_FORMAT( urr.time, '%Y%c%d%H%i' ) = #{item})
                    OR
                    (DATE_FORMAT( urr.time, '%Y%c%e%H%i00' ) = #{item})
                    OR
                    (DATE_FORMAT( urr.time, '%Y%m%d%H%i00' ) = #{item})
                    OR
                    (DATE_FORMAT( urr.time, '%Y%m%e%H%i00' ) = #{item})
                    OR
                    (DATE_FORMAT( urr.time, '%Y%c%d%H%i00' ) = #{item})
                    )
                </foreach>
            </if>
        </where>
    </select>

    <select id="findReceivingRecordsByPortfolioNo" resultType="UserReceivingRecordsResponse">
        SELECT
            urr.id,
            urr.no,
            urr.badge_type,
            urr.image,
            urr.upgrade_status,
            urr.upgrade_image,
            pi.`name` AS portfolioName,
            pi.start_time,
            pi.end_time,
            pi.price,
            pi.statement,
            pi.introduction,
            r.`name` AS repertoireName,
            pi.cover_front,
            pi.cover_reverse,
            pi.issued_quantity,
            urr.upgrade_status AS upgradeCount,
            urr.amount,
            urr.seat_number,
            urr.time,
            t.name AS theaterName,
            pi.id AS portfolio_info_id
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_portfolio_info AS pi ON pi.portfolio_id = urr.portfolio_id AND pi.`status` = 1
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = pi.theater_id
        WHERE
            urr.portfolio_no = #{portfolioNo}
            AND urr.badge_type = 1
    </select>

    <select id="findUserDigitalAvatar" resultType="UserReceivingRecordsResponse">
        SELECT
            *
        FROM
            (
            SELECT
                urr.id,
                ( SELECT id FROM t_user_adorn_collection WHERE user_receiving_records_id = urr.id AND upgrade_status = urr.upgrade_status ) AS adorn,
                urr.upgrade_image AS image,
                urr.create_time,
                urr.upgrade_status,
                r.`name` AS repertoireName
            FROM
                t_user_receiving_records AS urr
                LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            <where>
                urr.badge_type = 2
                AND urr.upgrade_status = 1
                AND urr.user_id = #{userId}
                <if test="keyword != null and keyword != ''">
                    AND r.`name` LIKE CONCAT('%',#{keyword},'%')
                </if>
            </where>
            UNION
            SELECT
                urr.id,
                ( SELECT id FROM t_user_adorn_collection WHERE user_receiving_records_id = urr.id AND upgrade_status = 0 ) AS adorn,
                urr.image,
                urr.create_time,
                '0' AS upgrade_status,
                r.`name` AS repertoireName
            FROM
                t_user_receiving_records AS urr
                LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            <where>
                urr.badge_type = 2
                AND urr.user_id = #{userId}
                <if test="keyword != null and keyword != ''">
                    AND r.`name` LIKE CONCAT('%',#{keyword},'%')
                </if>
            </where>
            ) AS urr
    </select>

    <select id="findDigitalAvatarCountByUser" resultType="java.lang.Long">
        SELECT
            (
                (
                SELECT
                    COUNT( 1 ) AS number
                FROM
                    t_user_receiving_records AS urr
                    LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
                <where>
                    urr.badge_type = 2
                    AND urr.upgrade_status = 1
                    AND urr.user_id = #{userId}
                </where>
                )
                +
                (
                SELECT
                    COUNT( 1 ) AS number
                FROM
                    t_user_receiving_records AS urr
                    LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
                <where>
                    urr.badge_type = 2
                    AND urr.user_id = #{userId}
                </where>
                )
            ) AS urr
    </select>

    <select id="findUserSouvenirBadge" resultType="UserReceivingRecordsResponse">
        SELECT
            urr.id,
            ( SELECT id FROM t_user_adorn_collection WHERE user_receiving_records_id = urr.id ) AS adorn,
            urr.image ,
            urr.create_time,
            t.`name` AS theaterName,
            sb.issued_quantity,
            t.city_id
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_souvenir_badge AS sb ON sb.id = urr.relation_id
            LEFT JOIN t_theater AS t ON t.id = urr.theater_id
        <where>
            urr.badge_type = 3
            AND urr.user_id = #{userId}
            <if test="keyword != null and keyword != ''">
                AND
                (
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (sb.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
        </where>
    </select>

    <select id="findUserLookCount" resultType="com.youying.system.domain.userreceivingrecords.UserGetResponse">
        SELECT
        COUNT( 1 ) AS number,
        user_id,
        IFNULL( u.amount, 0 ) AS price
        FROM
        t_user_receiving_records AS urr
        LEFT JOIN t_user AS u ON u.id = urr.user_id
        <where>
            urr.user_id = #{userId}
            AND badge_type = '1'
            <if test="code == '2'.toString()">
                AND theater_id = #{relationId}
            </if>
            <if test="code == '1'.toString()">
                AND repertoire_id = #{relationId}
            </if>
        </where>
        GROUP BY
        user_id
    </select>

    <select id="findUserLookAssignRepertoire" resultType="java.lang.Long">
        SELECT
        COUNT( 1 )
        FROM
        t_user_receiving_records AS urr
        <where>
            urr.badge_type = '1'
            AND urr.user_id = #{userId}
            <if test="repertoireInfoId != '0'.toString()">
                AND urr.repertoire_info_detail_id = #{repertoireInfoId}
                AND urr.theater_id = #{theaterId}
            </if>
            <if test="lookNumber != '0'.toString() and startTime != null and endTime != null">
                AND
                (
                SELECT
                COUNT( 1 )
                FROM
                t_user_receiving_records
                WHERE
                user_id = #{userId}
                AND badge_type = '1'
                AND theater_id = #{theaterId}
                AND DATE_FORMAT(urr.time,'%Y-%m-%e %H:%i:00') BETWEEN #{startTime} AND #{endTime}
                ) >= #{lookNumber}
            </if>
            <if test="rankMedalId != '0'.toString() and rankMedalInfoId != '0'.toString()">
                AND
                (
                SELECT
                COUNT( 1 )
                FROM
                t_user_push_collection AS upc
                LEFT JOIN t_rank_medal_info AS rm ON rm.id = upc.rank_medal_info_id
                WHERE
                upc.user_id = #{userId}
                AND upc.rank_medal_id = #{rankMedalId}
                AND upc.status = '1'
                AND rm.`name` > (SELECT `name` FROM t_rank_medal_info WHERE id = #{rankMedalInfoId})
                ) > 0
            </if>
        </where>
    </select>

    <select id="findElectronicTicketByUserId" resultType="ticketGroupResponse">
        SELECT
            urr.id AS userReceivingRecordsId,
            IFNULL(urr.upgrade_image,urr.image) AS image,
            urr.repertoire_id,
            r.cover_picture
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = urr.theater_id
        <where>
            urr.user_id = #{userId}
            AND urr.badge_type = 1
            <if test="status != null and status != ''">
                <choose>
                    <when test="status == '1'.toString()">
                        AND NOW() > urr.time
                    </when>
                    <when test="status == '2'.toString()">
                        AND urr.time > NOW()
                    </when>
                    <when test="status == '3'.toString()">
                        AND
                        (
                        NOW() > urr.time
                        OR
                        urr.time > NOW()
                        )
                    </when>
                    <when test="status == '4'.toString()">
                        AND urr.price = 0
                    </when>
                    <when test="status == '5'.toString()">
                        AND
                        (
                        NOW() > urr.time
                        OR
                        urr.price = 0
                        )
                    </when>
                    <when test="status == '6'.toString()">
                        AND
                        (
                        urr.time > NOW()
                        OR
                        urr.price = 0
                        )
                    </when>
                    <when test="status == '7'.toString()">
                        AND
                        (
                        NOW() > urr.time
                        OR
                        urr.time > NOW()
                        OR
                        urr.price = 0
                        )
                    </when>
                    <otherwise>
                        AND 0 = 1
                    </otherwise>
                </choose>
            </if>
            <if test="keyNameList != null and keyNameList.length > 0">
                AND
                <foreach item="item" index="index" collection="keyNameList" open="(" separator="OR" close=")">
                    (
                    r.`name` LIKE CONCAT('%',#{item},'%')
                    OR t.`name` LIKE CONCAT('%',#{item},'%')
                    OR urr.`time` LIKE CONCAT('%',#{item},'%')
                    OR (SELECT COUNT(1) FROM t_user_receiving_records_text WHERE user_receiving_records_id = urr.id AND actor_information LIKE CONCAT('%',#{item},'%') ) > 0
                    )
                </foreach>
            </if>
        </where>
        ORDER BY
            urr.create_time DESC , urr.id
    </select>

</mapper>

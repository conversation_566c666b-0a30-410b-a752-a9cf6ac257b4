<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.TicketGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ticketGroupMap" type="com.youying.system.domain.ticketgroup.TicketGroupResponse">
        <id column="id" property="id" />
        <result column="ticketGroupId" property="ticketGroupId"/>
        <result column="userId" property="userId"/>
        <collection property="electronicTicketImageList" select="electronicTicketImageQuery" column="{ticketGroupId=ticketGroupId,userId=userId}" ofType="string" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `name`, `type`, user_id, `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="electronicTicketImageQuery" resultType="string" >
        SELECT
            IFNULL( urr.upgrade_image, urr.image ) AS image
        FROM
            t_user_ticket_group AS utg
            LEFT JOIN t_user_receiving_records AS urr ON urr.id = utg.user_receiving_records_id
        WHERE
            utg.ticket_group_id = #{ticketGroupId}
            AND utg.user_id = #{userId}
        LIMIT 3
    </select>

    <select id="findTicketGroupCount" resultType="java.lang.Integer">
        SELECT
            COUNT( 1 )
        FROM
            t_ticket_group
        WHERE
            user_id = #{userId}
            AND type = #{dataType}
    </select>

    <select id="pull" resultType="com.youying.common.core.common.PullResponse">
        SELECT
            tg.id,
            tg.`name`
        FROM
            t_ticket_group AS tg
        <where>
            (tg.type = 0 OR tg.user_id = #{userId})
            AND tg.status = '1'
        </where>
        ORDER BY
            tg.type,
            tg.sort,
            tg.id
    </select>

    <select id="findTicketGroupList" resultMap="ticketGroupMap">
        SELECT
            tg.id AS ticketGroupId,
            tg.`name` AS ticketGroupName,
            tg.type,
            #{userId} AS userId,
            (SELECT COUNT(1) FROM t_user_ticket_group WHERE user_id = #{userId} AND ticket_group_id = tg.id) AS number
        FROM
            t_ticket_group AS tg
        <where>
            (tg.type = 0 OR tg.user_id = #{userId})
            AND tg.status = '1'
        </where>
        ORDER BY
            tg.type,
            tg.sort,
            tg.create_time DESC,
            tg.id
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.TheaterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Theater">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="phone" property="phone"/>
        <result column="prov_id" property="provId"/>
        <result column="city_id" property="cityId"/>
        <result column="area_id" property="areaId"/>
        <result column="address" property="address"/>
        <result column="cover_picture" property="coverPicture"/>
        <result column="pictures" property="pictures"/>
        <result column="good_rating_rate" property="goodRatingRate"/>
        <result column="focus_number" property="focusNumber"/>
        <result column="remark" property="remark"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="deleted" property="deleted"/>
        <result column="recommend" property="recommend"/>
        <result column="audit" property="audit"/>
        <result column="audit_pass_time" property="auditPassTime"/>
        <result column="reasons_rejection" property="reasonsRejection"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , `name`, contact_person, phone, prov_id, city_id, area_id, address, cover_picture, pictures, good_rating_rate, focus_number, remark, merchant_id, deleted, recommend, audit, audit_pass_time, reasons_rejection, `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="listByIndex" resultType="com.youying.system.domain.theater.TheaterResponse">
        SELECT
            t.id,
            t.`name`,
            t.cover_picture,
            t.pictures,
            t.merchant_id,
            t.focus_number,
            t.recommend,
            ( SELECT COUNT( 1 ) FROM t_user_treasure WHERE theater_id = t.id AND user_id = #{userId} ) AS fansFlag
        FROM
            t_theater AS t
        WHERE
            t.deleted = '1'
            AND t.audit = '2'
            AND t.`status` = '1'
    </select>

    <select id="listByPage" resultType="com.youying.system.domain.theater.TheaterResponse">
        SELECT
            t.id,
            t.`name`,
            t.cover_picture,
            t.pictures,
            t.focus_number,
            t.remark,
            t.merchant_id,
            t.prov_id,
            t.city_id,
            t.area_id,
            CONCAT( COALESCE(a.`fullname`,''), COALESCE(a1.`fullname`,''), COALESCE(a2.`fullname`,''), COALESCE(t.`address`,'') ) AS address,
            ( SELECT COUNT( 1 ) FROM t_user_treasure WHERE theater_id = t.id AND user_id = #{userId} ) AS fansFlag
        FROM
            t_theater AS t
            LEFT JOIN t_area AS a ON a.id = t.prov_id
            LEFT JOIN t_area AS a1 ON a1.id = t.city_id
            LEFT JOIN t_area AS a2 ON a2.id = t.area_id
        <where>
            t.deleted = '1'
            AND t.audit = '2'
            AND t.`status` = '1'
            <if test="keyword != null and keyword != ''">
                AND t.`name` LIKE CONCAT('%',#{keyword},'%')
            </if>
        </where>
        ORDER BY
            t.create_time DESC
    </select>

    <select id="details" resultType="com.youying.system.domain.theater.TheaterResponse">
        SELECT
            t.id,
            t.cover_picture,
            t.focus_number,
            t.pictures,
            t.`name`,
            t.merchant_id,
            CONCAT( COALESCE(a.`fullname`,''), COALESCE(a1.`fullname`,''), COALESCE(a2.`fullname`,''), COALESCE(t.`address`,'') ) AS address,
            ( SELECT COUNT( 1 ) FROM t_user_treasure WHERE theater_id = t.id AND user_id = #{userId} ) AS fansFlag,
            ( SELECT COUNT( 1 ) FROM t_kudos AS k WHERE k.theater_id = t.id AND k.type = 1 ) AS likeCount,
            ( SELECT COUNT( 1 ) FROM t_kudos AS k WHERE k.theater_id = t.id AND k.type = 0 ) AS dislikeCount
        FROM
            t_theater AS t
            LEFT JOIN t_area AS a ON a.id = t.prov_id
            LEFT JOIN t_area AS a1 ON a1.id = t.city_id
            LEFT JOIN t_area AS a2 ON a2.id = t.area_id
        WHERE
            t.id = #{id}
            AND t.deleted = '1'
            AND t.audit = '2'
            AND t.`status` = '1'
        GROUP BY
            t.id
    </select>

    <select id="findTheaterDisplay" resultType="com.youying.system.domain.repertoire.RepertoireDisplayResponse">
        SELECT
            sb.cover_picture AS souvenirBadgeUrl,
            DATE(sb.audit_pass_time) AS auditPassTime
        FROM
            t_souvenir_badge AS sb
            LEFT JOIN t_theater AS t ON t.id = sb.theater_id
        <where>
            sb.`status` = '1'
            AND sb.theater_id = #{theaterId}
            AND sb.audit = '2'
            AND sb.look_status = '1'
            AND t.deleted = '1'
            AND t.`status` = '1'
        </where>
        ORDER BY
            `auditPassTime` DESC , sb.id
    </select>

</mapper>

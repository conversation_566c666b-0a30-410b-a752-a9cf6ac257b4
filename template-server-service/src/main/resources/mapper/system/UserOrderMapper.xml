<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserOrderMapper">

    <resultMap id="userOrderMap" type="com.youying.system.domain.userorder.UserOrderResponse">
        <id column="id" property="id"/>
        <result column="user_receiving_records_id" property="userReceivingRecordsId"/>
        <collection property="userReceivingRecordList" select="userReceivingRecordQuery" column="user_receiving_records_id=user_receiving_records_id" ofType="com.youying.common.core.domain.entity.UserReceivingRecords" />
    </resultMap>

    <update id="updateUserOrder">
        update t_user_order
        <set>
            <if test="userOrder.payStatus != null">pay_status = #{userOrder.payStatus},</if>
            <if test="userOrder.payTime != null">pay_time = #{userOrder.payTime},</if>
            <if test="userOrder.transactionId != null and userOrder.transactionId != ''">transaction_id = #{userOrder.transactionId},</if>
        </set>
        where id = #{userOrder.id}
    </update>

    <select id="userReceivingRecordQuery" resultType="UserReceivingRecords">
        SELECT
            urr.id,
            urr.image,
            urr.upgrade_image,
            urr.badge_type
        FROM
            t_user_receiving_records AS urr
        WHERE
            urr.portfolio_no = (SELECT portfolio_no FROM t_user_receiving_records WHERE id = #{user_receiving_records_id})
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserOrder">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="user_id" property="userId"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="order_no" property="orderNo"/>
        <result column="prepay_id" property="prepayId"/>
        <result column="pay_price" property="payPrice"/>
        <result column="refund_price" property="refundPrice"/>
        <result column="pay_status" property="payStatus"/>
        <result column="refund_status" property="refundStatus"/>
        <result column="pay_time" property="payTime"/>
        <result column="refund_time" property="refundTime"/>
        <result column="user_receiving_records_id" property="userReceivingRecordsId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="relation_id" property="relationId"/>
        <result column="badge_type" property="badgeType"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , merchant_id, user_id, transaction_id, order_no, prepay_id, pay_price, refund_price, pay_status, refund_status, pay_time, refund_time, user_receiving_records_id, theater_id, repertoire_id, relation_id, badge_type, create_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.userorder.UserOrderResponse">
        SELECT
            uo.id,
            uo.prepay_id,
            uo.pay_price,
            uo.pay_status,
            uo.create_time,
            r.`name` AS repertoireName,
            r.cover_picture AS repertoireCoverPicture
        FROM
            t_user_order AS uo
            LEFT JOIN t_repertoire AS r ON r.id = uo.repertoire_id
            LEFT JOIN t_repertoire_ticket AS rt ON (rt.id = uo.relation_id AND badge_type = 1)
        <where>
            uo.user_id = #{userId}
            <if test="pageDomain.keyword != null and pageDomain.keyword != ''">
                AND r.`name` LIKE CONCAT('%',#{pageDomain.keyword},'%')
            </if>
        </where>
            ORDER By uo.create_time DESC
    </select>

    <select id="details" resultMap="userOrderMap">
        SELECT
            uo.id,
            uo.merchant_id,
            uo.user_id,
            uo.transaction_id,
            uo.order_no,
            uo.prepay_id,
            uo.pay_price,
            uo.refund_price,
            uo.pay_status,
            uo.refund_status,
            uo.pay_time,
            uo.refund_time,
            uo.user_receiving_records_id,
            uo.theater_id,
            uo.repertoire_id,
            uo.relation_id,
            uo.badge_type,
            uo.create_time ,
            p.`name` AS portfolioName,
            p.price,
            r.`name` AS repertoireName,
            r.cover_picture AS repertoireCoverPicture
        FROM
            t_user_order AS uo
            LEFT JOIN t_portfolio AS p ON p.id = uo.portfolio_id
            LEFT JOIN t_repertoire AS r ON r.id = uo.repertoire_id
        WHERE
            uo.id = #{id}
            AND uo.user_id = #{userId}
    </select>

    <select id="findUserOrderNotPay" resultType="com.youying.common.core.domain.entity.UserOrder">
        SELECT
            *
        FROM
            t_user_order AS uo
        WHERE
            uo.pay_status = 0
            AND NOW() >= create_time + INTERVAL 30 MINUTE
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserPushCollectionMapper">

    <resultMap id="userPushCollectionMap" type="com.youying.system.domain.userpushcollection.UserPushCollectionResponse">
        <id column="id" property="id"/>
        <result column="rank_medal_info_id" property="rankMedalInfoId"/>
        <result column="souvenir_badge_id" property="souvenirBadgeId"/>
        <association property="rankMedalResponse" select="rankMedalResponseQuery" column="rank_medal_info_id" javaType="com.youying.system.domain.rankmedalinfo.RankMedalInfoResponse" />
        <association property="souvenirBadge" select="souvenirBadgeQuery" column="souvenir_badge_id" javaType="com.youying.system.domain.souvenirbadge.SouvenirBadgeResponse" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserPushCollection">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="rank_medal_id" property="rankMedalId"/>
        <result column="rank_medal_info_id" property="rankMedalInfoId"/>
        <result column="souvenir_badge_id" property="souvenirBadgeId"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, theater_id, repertoire_id, rank_medal_id, rank_medal_info_id, souvenir_badge_id, `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="rankMedalResponseQuery" resultType="com.youying.system.domain.rankmedalinfo.RankMedalInfoResponse">
        SELECT
            rmi.id,
            rmi.`name` AS rankMedalLevel,
            rmi.rank_medal_name,
            rmi.color,
            r.`name` AS repertoireName,
            t.`name` AS theaterName
        FROM
            t_rank_medal_info AS rmi
            LEFT JOIN t_rank_medal AS rm ON rm.id = rmi.rank_medal_id
            LEFT JOIN t_theater AS t ON t.id = rmi.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = rmi.repertoire_id
        WHERE
            rmi.id = #{rank_medal_info_id}
    </select>

    <select id="souvenirBadgeQuery" resultType="com.youying.system.domain.souvenirbadge.SouvenirBadgeResponse">
        SELECT
            sb.id,
            t.`name` as theaterName,
            sb.cover_picture
        FROM
            t_souvenir_badge AS sb
            LEFT JOIN t_theater AS t ON t.id = sb.theater_id
        WHERE
            sb.id = #{souvenir_badge_id}
    </select>

    <select id="listByPage" resultMap="userPushCollectionMap">
        SELECT
            upc.id,
            upc.user_id,
            upc.theater_id,
            upc.repertoire_id,
            upc.rank_medal_id,
            upc.rank_medal_info_id,
            upc.souvenir_badge_id,
            upc.`status`,
            upc.create_by,
            upc.create_time,
            upc.update_by,
            upc.update_time
        FROM
            t_user_push_collection AS upc
            LEFT JOIN t_souvenir_badge AS sb ON sb.id = upc.souvenir_badge_id
        WHERE
            upc.user_id = #{userId}
            AND upc.`status` = 0
            AND
            (
            upc.souvenir_badge_id > 0
            AND sb.sold_out = 0
            AND sb.`status` = 1
            AND NOW() BETWEEN sb.start_time
            AND sb.end_time
            AND sb.issued_quantity > ( SELECT COUNT( 1 ) FROM t_user_push_collection WHERE souvenir_badge_id = upc.souvenir_badge_id AND `status` = 1 )
            )
    </select>

</mapper>
